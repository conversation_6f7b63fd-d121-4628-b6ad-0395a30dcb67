import { supabase } from './supabase';
import { Channel, Folder, Idea } from '../types';

export const dataLoader = {
  async loadAllData() {
    try {
      const [channelsResult, foldersResult, ideasResult] = await Promise.all([
        supabase.from('youtubetool_channels').select('*').order('created_at', { ascending: false }),
        supabase.from('youtubetool_folders').select('*').order('created_at', { ascending: false }),
        supabase.from('youtubetool_ideas').select('*').order('created_at', { ascending: false })
      ]);

      // Map database columns to TypeScript interfaces
      const channels: Channel[] = (channelsResult.data || []).map(ch => ({
        id: ch.id,
        name: ch.name,
        description: '', // Not stored in DB
        color: ch.color,
        defaultSettings: ch.defaultsettings || {},
        createdAt: ch.created_at,
        updatedAt: ch.updated_at
      }));

      const folders: Folder[] = (foldersResult.data || []).map(f => ({
        id: f.id,
        name: f.name,
        channelId: f.channel_id,
        createdAt: f.created_at,
        updatedAt: f.updated_at
      }));

      const ideas: Idea[] = (ideasResult.data || []).map(i => ({
        id: i.id,
        name: i.name,
        channelId: i.channel_id,
        folderId: i.folder_id,
        priority: i.priority,
        status: i.status,
        content: i.content || {},
        tags: [], // Not stored in database
        promptHistory: [],
        createdAt: i.created_at,
        updatedAt: i.updated_at
      }));

      if (channelsResult.error) console.error('Error loading channels:', channelsResult.error);
      if (foldersResult.error) console.error('Error loading folders:', foldersResult.error);
      if (ideasResult.error) console.error('Error loading ideas:', ideasResult.error);

      return { channels, folders, ideas };
    } catch (error) {
      console.error('Error loading data:', error);
      return { channels: [], folders: [], ideas: [] };
    }
  },

  async createChannel(channel: Channel): Promise<Channel | null> {
    console.log('Creating channel:', channel);
    
    // Check authentication
    const { data: { session } } = await supabase.auth.getSession();
    console.log('Current session:', session);
    
    const { data, error } = await supabase
      .from('youtubetool_channels')
      .insert([{
        id: channel.id,
        name: channel.name,
        color: channel.color,
        defaultsettings: channel.defaultSettings,
        created_at: channel.createdAt,
        updated_at: channel.updatedAt
      }])
      .select()
      .single();

    console.log('Channel creation result:', { data, error });

    if (error) {
      console.error('Error creating channel:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      return null;
    }
    return {
      id: data.id,
      name: data.name,
      description: '', // Not stored in DB
      color: data.color,
      defaultSettings: data.defaultsettings,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  },

  async updateChannel(channel: Channel): Promise<Channel | null> {
    const { data, error } = await supabase
      .from('youtubetool_channels')
      .update({
        name: channel.name,
        color: channel.color,
        defaultsettings: channel.defaultSettings,
        updated_at: new Date().toISOString()
      })
      .eq('id', channel.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating channel:', error);
      return null;
    }
    return {
      id: data.id,
      name: data.name,
      description: '', // Not stored in DB
      color: data.color,
      defaultSettings: data.defaultsettings,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  },

  async deleteChannel(channelId: string): Promise<boolean> {
    const { error } = await supabase
      .from('youtubetool_channels')
      .delete()
      .eq('id', channelId);

    if (error) {
      console.error('Error deleting channel:', error);
      return false;
    }
    return true;
  },

  async createFolder(folder: Folder): Promise<Folder | null> {
    console.log('Creating folder:', folder);
    
    const { data, error } = await supabase
      .from('youtubetool_folders')
      .insert([{
        id: folder.id,
        name: folder.name,
        channel_id: folder.channelId,
        created_at: folder.createdAt,
        updated_at: folder.updatedAt
      }])
      .select()
      .single();

    console.log('Folder creation result:', { data, error });

    if (error) {
      console.error('Error creating folder:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      return null;
    }
    return {
      id: data.id,
      name: data.name,
      channelId: data.channel_id,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  },

  async updateFolder(folder: Folder): Promise<Folder | null> {
    const { data, error } = await supabase
      .from('youtubetool_folders')
      .update({
        name: folder.name,
        updated_at: new Date().toISOString()
      })
      .eq('id', folder.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating folder:', error);
      return null;
    }
    return {
      id: data.id,
      name: data.name,
      channelId: data.channel_id,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  },

  async deleteFolder(folderId: string): Promise<boolean> {
    const { error } = await supabase
      .from('youtubetool_folders')
      .delete()
      .eq('id', folderId);

    if (error) {
      console.error('Error deleting folder:', error);
      return false;
    }
    return true;
  },

  async createIdea(idea: Idea): Promise<Idea | null> {
    console.log('Creating idea:', idea);
    
    const { data, error } = await supabase
      .from('youtubetool_ideas')
      .insert([{
        id: idea.id,
        name: idea.name,
        channel_id: idea.channelId,
        folder_id: idea.folderId,
        priority: idea.priority,
        status: idea.status,
        content: idea.content,
        // tags: idea.tags, // Column doesn't exist in database
        created_at: idea.createdAt,
        updated_at: idea.updatedAt
      }])
      .select()
      .single();

    console.log('Idea creation result:', { data, error });

    if (error) {
      console.error('Error creating idea:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      return null;
    }
    return {
      id: data.id,
      name: data.name,
      channelId: data.channel_id,
      folderId: data.folder_id,
      priority: data.priority,
      status: data.status,
      content: data.content,
      tags: [], // Not stored in database
      promptHistory: [],
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  },

  async updateIdea(idea: Idea): Promise<Idea | null> {
    const { data, error } = await supabase
      .from('youtubetool_ideas')
      .update({
        name: idea.name,
        priority: idea.priority,
        status: idea.status,
        content: idea.content,
        tags: idea.tags,
        updated_at: new Date().toISOString()
      })
      .eq('id', idea.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating idea:', error);
      return null;
    }
    return {
      id: data.id,
      name: data.name,
      channelId: data.channel_id,
      folderId: data.folder_id,
      priority: data.priority,
      status: data.status,
      content: data.content,
      tags: [], // Not stored in database
      promptHistory: [],
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  },

  async deleteIdea(ideaId: string): Promise<boolean> {
    const { error } = await supabase
      .from('youtubetool_ideas')
      .delete()
      .eq('ideaId', ideaId);

    if (error) {
      console.error('Error deleting idea:', error);
      return false;
    }
    return true;
  }
};
