// Browser-compatible YouTube API implementation

// YouTube API configuration
const YOUTUBE_API_KEY = import.meta.env.VITE_YOUTUBE_API_KEY;
const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = import.meta.env.VITE_GOOGLE_CLIENT_SECRET;
const REDIRECT_URI = import.meta.env.VITE_GOOGLE_REDIRECT_URI || 'http://localhost:5173/auth/callback';

// YouTube API scopes
const SCOPES = [
  'https://www.googleapis.com/auth/youtube.readonly',
  'https://www.googleapis.com/auth/youtube.upload',
  'https://www.googleapis.com/auth/youtube.force-ssl'
];

export interface YouTubeChannel {
  id: string;
  title: string;
  description: string;
  thumbnails: {
    default: { url: string };
    medium: { url: string };
    high: { url: string };
  };
  subscriberCount: string;
  videoCount: string;
  viewCount: string;
  customUrl?: string;
}

export interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnails: {
    default: { url: string };
    medium: { url: string };
    high: { url: string };
  };
  publishedAt: string;
  viewCount: string;
  likeCount: string;
  commentCount: string;
  duration: string;
  tags: string[];
}

export interface YouTubePlaylist {
  id: string;
  title: string;
  description: string;
  thumbnails: {
    default: { url: string };
    medium: { url: string };
    high: { url: string };
  };
  itemCount: number;
  publishedAt: string;
}

class YouTubeApiService {
  private accessToken: string | null = null;

  constructor() {
    // Load stored access token
    this.accessToken = localStorage.getItem('youtube_access_token');
  }

  // Generate OAuth URL for user authentication
  getAuthUrl(): string {
    const params = new URLSearchParams({
      client_id: GOOGLE_CLIENT_ID,
      redirect_uri: REDIRECT_URI,
      scope: SCOPES.join(' '),
      response_type: 'code',
      access_type: 'offline',
      prompt: 'consent'
    });

    return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
  }

  // Exchange authorization code for tokens
  async getTokens(code: string) {
    try {
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: GOOGLE_CLIENT_ID,
          client_secret: GOOGLE_CLIENT_SECRET,
          code: code,
          grant_type: 'authorization_code',
          redirect_uri: REDIRECT_URI,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const tokens = await response.json();

      // Store access token
      if (tokens.access_token) {
        this.accessToken = tokens.access_token;
        localStorage.setItem('youtube_access_token', tokens.access_token);

        // Store refresh token if available
        if (tokens.refresh_token) {
          localStorage.setItem('youtube_refresh_token', tokens.refresh_token);
        }
      }

      return tokens;
    } catch (error) {
      console.error('Error getting tokens:', error);
      throw error;
    }
  }

  // Set stored tokens
  setTokens(tokens: any) {
    if (tokens.access_token) {
      this.accessToken = tokens.access_token;
      localStorage.setItem('youtube_access_token', tokens.access_token);
    }
    if (tokens.refresh_token) {
      localStorage.setItem('youtube_refresh_token', tokens.refresh_token);
    }
  }

  // Get user's YouTube channels
  async getMyChannels(): Promise<YouTubeChannel[]> {
    if (!this.accessToken) {
      throw new Error('Not authenticated');
    }

    try {
      const url = new URL('https://www.googleapis.com/youtube/v3/channels');
      url.searchParams.set('part', 'snippet,statistics,brandingSettings');
      url.searchParams.set('mine', 'true');
      url.searchParams.set('key', YOUTUBE_API_KEY);

      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      return data.items.map((item: any) => ({
        id: item.id,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnails: item.snippet.thumbnails,
        subscriberCount: item.statistics.subscriberCount,
        videoCount: item.statistics.videoCount,
        viewCount: item.statistics.viewCount,
        customUrl: item.snippet.customUrl
      }));
    } catch (error) {
      console.error('Error fetching channels:', error);
      throw error;
    }
  }

  // Get channel videos
  async getChannelVideos(channelId: string, maxResults: number = 50): Promise<YouTubeVideo[]> {
    if (!this.accessToken) {
      throw new Error('Not authenticated');
    }

    try {
      // First get the uploads playlist ID
      const channelUrl = new URL('https://www.googleapis.com/youtube/v3/channels');
      channelUrl.searchParams.set('part', 'contentDetails');
      channelUrl.searchParams.set('id', channelId);
      channelUrl.searchParams.set('key', YOUTUBE_API_KEY);

      const channelResponse = await fetch(channelUrl.toString(), {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
      });

      if (!channelResponse.ok) {
        throw new Error(`HTTP error! status: ${channelResponse.status}`);
      }

      const channelData = await channelResponse.json();
      const uploadsPlaylistId = channelData.items[0]?.contentDetails?.relatedPlaylists?.uploads;

      if (!uploadsPlaylistId) {
        throw new Error('No uploads playlist found');
      }

      // Get videos from uploads playlist
      const playlistUrl = new URL('https://www.googleapis.com/youtube/v3/playlistItems');
      playlistUrl.searchParams.set('part', 'snippet');
      playlistUrl.searchParams.set('playlistId', uploadsPlaylistId);
      playlistUrl.searchParams.set('maxResults', maxResults.toString());
      playlistUrl.searchParams.set('key', YOUTUBE_API_KEY);

      const playlistResponse = await fetch(playlistUrl.toString(), {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
      });

      if (!playlistResponse.ok) {
        throw new Error(`HTTP error! status: ${playlistResponse.status}`);
      }

      const playlistData = await playlistResponse.json();
      const videoIds = playlistData.items.map((item: any) => item.snippet.resourceId.videoId);

      if (videoIds.length === 0) {
        return [];
      }

      // Get detailed video information
      const videosUrl = new URL('https://www.googleapis.com/youtube/v3/videos');
      videosUrl.searchParams.set('part', 'snippet,statistics,contentDetails');
      videosUrl.searchParams.set('id', videoIds.join(','));
      videosUrl.searchParams.set('key', YOUTUBE_API_KEY);

      const videosResponse = await fetch(videosUrl.toString(), {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
      });

      if (!videosResponse.ok) {
        throw new Error(`HTTP error! status: ${videosResponse.status}`);
      }

      const videosData = await videosResponse.json();

      return videosData.items.map((item: any) => ({
        id: item.id,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnails: item.snippet.thumbnails,
        publishedAt: item.snippet.publishedAt,
        viewCount: item.statistics.viewCount || '0',
        likeCount: item.statistics.likeCount || '0',
        commentCount: item.statistics.commentCount || '0',
        duration: item.contentDetails.duration,
        tags: item.snippet.tags || []
      }));
    } catch (error) {
      console.error('Error fetching channel videos:', error);
      throw error;
    }
  }

  // Get channel playlists
  async getChannelPlaylists(channelId: string, maxResults: number = 50): Promise<YouTubePlaylist[]> {
    if (!this.accessToken) {
      throw new Error('Not authenticated');
    }

    try {
      const url = new URL('https://www.googleapis.com/youtube/v3/playlists');
      url.searchParams.set('part', 'snippet,contentDetails');
      url.searchParams.set('channelId', channelId);
      url.searchParams.set('maxResults', maxResults.toString());
      url.searchParams.set('key', YOUTUBE_API_KEY);

      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      return data.items.map((item: any) => ({
        id: item.id,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnails: item.snippet.thumbnails,
        itemCount: item.contentDetails.itemCount,
        publishedAt: item.snippet.publishedAt
      }));
    } catch (error) {
      console.error('Error fetching playlists:', error);
      throw error;
    }
  }

  // Search for videos (useful for content research)
  async searchVideos(query: string, maxResults: number = 25): Promise<YouTubeVideo[]> {
    try {
      const searchUrl = new URL('https://www.googleapis.com/youtube/v3/search');
      searchUrl.searchParams.set('part', 'snippet');
      searchUrl.searchParams.set('q', query);
      searchUrl.searchParams.set('type', 'video');
      searchUrl.searchParams.set('maxResults', maxResults.toString());
      searchUrl.searchParams.set('key', YOUTUBE_API_KEY);

      const searchResponse = await fetch(searchUrl.toString());

      if (!searchResponse.ok) {
        throw new Error(`HTTP error! status: ${searchResponse.status}`);
      }

      const searchData = await searchResponse.json();
      const videoIds = searchData.items.map((item: any) => item.id.videoId);

      if (videoIds.length === 0) {
        return [];
      }

      // Get detailed video information
      const videosUrl = new URL('https://www.googleapis.com/youtube/v3/videos');
      videosUrl.searchParams.set('part', 'snippet,statistics,contentDetails');
      videosUrl.searchParams.set('id', videoIds.join(','));
      videosUrl.searchParams.set('key', YOUTUBE_API_KEY);

      const videosResponse = await fetch(videosUrl.toString());

      if (!videosResponse.ok) {
        throw new Error(`HTTP error! status: ${videosResponse.status}`);
      }

      const videosData = await videosResponse.json();

      return videosData.items.map((item: any) => ({
        id: item.id,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnails: item.snippet.thumbnails,
        publishedAt: item.snippet.publishedAt,
        viewCount: item.statistics.viewCount || '0',
        likeCount: item.statistics.likeCount || '0',
        commentCount: item.statistics.commentCount || '0',
        duration: item.contentDetails.duration,
        tags: item.snippet.tags || []
      }));
    } catch (error) {
      console.error('Error searching videos:', error);
      throw error;
    }
  }

  // Get video analytics (requires YouTube Analytics API)
  async getVideoAnalytics(videoId: string, startDate: string, endDate: string) {
    // This would require YouTube Analytics API
    // For now, return basic stats from the video details
    try {
      const url = new URL('https://www.googleapis.com/youtube/v3/videos');
      url.searchParams.set('part', 'statistics');
      url.searchParams.set('id', videoId);
      url.searchParams.set('key', YOUTUBE_API_KEY);

      const response = await fetch(url.toString());

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.items[0]?.statistics || {};
    } catch (error) {
      console.error('Error fetching video analytics:', error);
      throw error;
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  // Refresh access token
  async refreshToken() {
    const refreshToken = localStorage.getItem('youtube_refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: GOOGLE_CLIENT_ID,
          client_secret: GOOGLE_CLIENT_SECRET,
          refresh_token: refreshToken,
          grant_type: 'refresh_token',
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const tokens = await response.json();

      if (tokens.access_token) {
        this.accessToken = tokens.access_token;
        localStorage.setItem('youtube_access_token', tokens.access_token);
      }

      return tokens;
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  }
}

export const youtubeApi = new YouTubeApiService();
