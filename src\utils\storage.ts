import { Channel, Folder, Idea, GenerationPreset } from '../types';
import { supabase } from './supabase';

export const storage = {
  // Channels
  getChannels: async (): Promise<Channel[]> => {
    const { data, error } = await supabase
      .from('youtubetool_channels')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching channels:', error);
      return [];
    }
    return data || [];
  },

  saveChannels: async (channels: Channel[]): Promise<void> => {
    // This method is deprecated - use individual channel operations instead
    console.warn('saveChannels is deprecated - use individual channel operations');
  },

  // Folders
  getFolders: async (): Promise<Folder[]> => {
    const { data, error } = await supabase
      .from('youtubetool_folders')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching folders:', error);
      return [];
    }
    return data || [];
  },

  saveFolders: async (folders: Folder[]): Promise<void> => {
    // This method is deprecated - use individual folder operations instead
    console.warn('saveFolders is deprecated - use individual folder operations');
  },

  // Ideas
  getIdeas: async (): Promise<Idea[]> => {
    const { data, error } = await supabase
      .from('youtubetool_ideas')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching ideas:', error);
      return [];
    }
    return data || [];
  },

  saveIdeas: async (ideas: Idea[]): Promise<void> => {
    // This method is deprecated - use individual idea operations instead
    console.warn('saveIdeas is deprecated - use individual idea operations');
  },

  // Presets - Keep in localStorage for now as they're user-specific
  getPresets: (): GenerationPreset[] => {
    const data = localStorage.getItem('youtube_empire_presets');
    return data ? JSON.parse(data) : [];
  },

  savePresets: (presets: GenerationPreset[]) => {
    localStorage.setItem('youtube_empire_presets', JSON.stringify(presets));
  },

  // App Settings - Keep in localStorage as they're user-specific
  getSettings: () => {
    const data = localStorage.getItem('youtube_empire_settings');
    return data ? JSON.parse(data) : { darkMode: false, apiKey: '' };
  },

  saveSettings: (settings: any) => {
    localStorage.setItem('youtube_empire_settings', JSON.stringify(settings));
  },
};
