import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, Edit, RefreshCw, Copy, Download, Settings, Lightbulb, FileText, Image, Video, Loader2, Check, X, Sparkles, Type, SkipForward
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { AIService, createPrompt } from '../../utils/ai';
import { Idea } from '../../types';

const IdeaView: React.FC = () => {
  const { state, dispatch } = useApp();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStep, setGenerationStep] = useState<'title' | 'thumbnail' | 'script' | null>(null);
  const [selectedTitle, setSelectedTitle] = useState<string | null>(null);
  const [manualThumbnail, setManualThumbnail] = useState('');
  const [isEditingThumbnail, setIsEditingThumbnail] = useState(false);

  const currentIdea = state.ideas.find(i => i.id === state.selectedIdeaId);
  const currentFolder = state.folders.find(f => f.id === currentIdea?.folderId);
  const currentChannel = state.channels.find(ch => ch.id === currentIdea?.channelId);

  useEffect(() => {
    if (currentIdea?.content.title) {
      setSelectedTitle(currentIdea.content.title);
    } else {
      setSelectedTitle(null);
    }
  }, [currentIdea]);

  if (!currentIdea || !currentFolder || !currentChannel) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <p className="text-gray-500 dark:text-gray-400">Idea not found. Please select an idea.</p>
      </div>
    );
  }

  const handleGenerate = async (step: 'title' | 'thumbnail' | 'script') => {
    if (!state.apiKey && import.meta.env.VITE_OPENROUTER_API_KEY === undefined) {
      alert('Please set your OpenRouter API key in settings first');
      return;
    }

    setIsGenerating(true);
    setGenerationStep(step);

    try {
      const aiService = new AIService(state.apiKey || import.meta.env.VITE_OPENROUTER_API_KEY);
      let updatedIdea: Idea;

      if (step === 'title') {
        const result = await aiService.generateTitle(currentIdea.name, currentChannel.defaultSettings);
        updatedIdea = {
          ...currentIdea,
          content: { ...currentIdea.content, generatedTitles: result, title: undefined },
          status: 'title',
          updatedAt: new Date().toISOString()
        };
      } else if (step === 'thumbnail') {
        const result = await aiService.generateThumbnailDescription(currentIdea.content.title!, currentChannel.defaultSettings);
        updatedIdea = {
          ...currentIdea,
          content: { ...currentIdea.content, thumbnailDescription: result },
          status: 'script',
          updatedAt: new Date().toISOString()
        };
      } else { // script
        const result = await aiService.generateScript(currentIdea.content.title!, currentChannel.defaultSettings);
        updatedIdea = {
          ...currentIdea,
          content: { ...currentIdea.content, script: result },
          status: 'script',
          updatedAt: new Date().toISOString()
        };
      }
      dispatch({ type: 'UPDATE_IDEA', payload: updatedIdea });
    } catch (error) {
      console.error('Generation failed:', error);
      alert('Failed to generate content. Please try again.');
    } finally {
      setIsGenerating(false);
      setGenerationStep(null);
    }
  };

  const handleConfirmTitle = () => {
    if (!selectedTitle) return;
    const updatedIdea = {
      ...currentIdea,
      content: { ...currentIdea.content, title: selectedTitle },
      status: 'thumbnail' as const,
      updatedAt: new Date().toISOString()
    };
    dispatch({ type: 'UPDATE_IDEA', payload: updatedIdea });
  };

  const handleUpdateStatus = (status: Idea['status']) => {
    dispatch({ type: 'UPDATE_IDEA', payload: { ...currentIdea, status, updatedAt: new Date().toISOString() } });
  };

  const handleSaveManualThumbnail = () => {
    const updatedIdea = {
      ...currentIdea,
      content: { ...currentIdea.content, thumbnailDescription: manualThumbnail },
      status: 'script' as const,
      updatedAt: new Date().toISOString()
    };
    dispatch({ type: 'UPDATE_IDEA', payload: updatedIdea });
    setIsEditingThumbnail(false);
  };
  
  const getStatusColor = (status: string) => ({
    idea: 'text-gray-500 bg-gray-100 dark:bg-gray-700/50 dark:text-gray-400',
    title: 'text-blue-500 bg-blue-100 dark:bg-blue-900/50 dark:text-blue-300',
    thumbnail: 'text-yellow-500 bg-yellow-100 dark:bg-yellow-900/50 dark:text-yellow-300',
    script: 'text-purple-500 bg-purple-100 dark:bg-purple-900/50 dark:text-purple-300',
    done: 'text-green-500 bg-green-100 dark:bg-green-900/50 dark:text-green-300'
  }[status] || 'text-gray-500 bg-gray-100 dark:bg-gray-700/50 dark:text-gray-400');

  const getPriorityColor = (priority: string) => ({
    high: 'text-red-500 bg-red-100 dark:bg-red-900/50 dark:text-red-300',
    medium: 'text-yellow-500 bg-yellow-100 dark:bg-yellow-900/50 dark:text-yellow-300',
    low: 'text-green-500 bg-green-100 dark:bg-green-900/50 dark:text-green-300'
  }[priority] || 'text-gray-500 bg-gray-100 dark:bg-gray-700/50 dark:text-gray-400');

  const progressSteps = [
    { key: 'idea', label: 'Idea', icon: Lightbulb },
    { key: 'title', label: 'Title', icon: FileText },
    { key: 'thumbnail', label: 'Thumbnail', icon: Image },
    { key: 'script', label: 'Script', icon: Video },
    { key: 'done', label: 'Done', icon: Check }
  ];
  const currentStepIndex = progressSteps.findIndex(step => step.key === currentIdea.status);

  return (
    <div className="h-full overflow-y-auto bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button onClick={() => dispatch({ type: 'SELECT_IDEA', payload: null })} className="p-2 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <div className="flex items-center space-x-2">
                <h1 className="text-3xl font-bold">{currentIdea.name}</h1>
                <button className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"><Edit className="w-4 h-4" /></button>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                <div className="w-2 h-2 rounded-full" style={{ backgroundColor: currentChannel.color }} />
                <span>{currentChannel.name}</span><span>•</span><span>{currentFolder.name}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(currentIdea.priority)}`}>{currentIdea.priority} priority</span>
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(currentIdea.status)}`}>{currentIdea.status}</span>
            <button className="p-2 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"><Settings className="w-5 h-5" /></button>
          </div>
        </div>

        {/* Progress Stepper */}
        <div className="mb-10">
          <div className="flex items-center">
            {progressSteps.map((step, index) => (
              <React.Fragment key={step.key}>
                <div className="flex flex-col items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${index <= currentStepIndex ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500'}`}>
                    <step.icon className="w-5 h-5" />
                  </div>
                  <span className={`text-sm mt-2 font-medium ${index <= currentStepIndex ? 'text-blue-500' : 'text-gray-500 dark:text-gray-400'}`}>{step.label}</span>
                </div>
                {index < progressSteps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-2 transition-colors ${index < currentStepIndex ? 'bg-blue-500' : 'bg-gray-200 dark:bg-gray-700'}`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Content Sections */}
        <div className="space-y-6">
          {/* Title Section */}
          <AnimatePresence>
            {(currentIdea.status === 'idea' || currentIdea.status === 'title') && (
              <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -10 }} className="bg-white dark:bg-gray-800/50 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold flex items-center space-x-2"><FileText className="w-5 h-5 text-blue-500" /><span>Video Title</span></h3>
                  <button onClick={() => handleGenerate('title')} disabled={isGenerating} className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 text-sm">
                    {isGenerating && generationStep === 'title' ? <Loader2 className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
                    <span>{currentIdea.content.generatedTitles ? 'Regenerate' : 'Generate Titles'}</span>
                  </button>
                </div>
                {isGenerating && generationStep === 'title' && <p className="text-center text-gray-500 dark:text-gray-400 py-8">Generating titles...</p>}
                {!isGenerating && !currentIdea.content.generatedTitles && <div className="p-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-center text-gray-500 dark:text-gray-400">Click "Generate Titles" to create 5 AI-powered video titles.</div>}
                {currentIdea.content.generatedTitles && (
                  <div className="space-y-3">
                    {currentIdea.content.generatedTitles.map((title, i) => (
                      <div key={i} onClick={() => setSelectedTitle(title)} className={`p-4 rounded-lg cursor-pointer transition-all border-2 ${selectedTitle === title ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30' : 'border-transparent hover:bg-gray-100 dark:hover:bg-gray-700/50'}`}>
                        <p className="font-medium">{title}</p>
                      </div>
                    ))}
                    <div className="flex justify-end pt-2">
                      <button onClick={handleConfirmTitle} disabled={!selectedTitle} className="bg-green-500 hover:bg-green-600 disabled:bg-green-300 disabled:cursor-not-allowed text-white px-5 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2">
                        <span>Confirm Title</span><Check className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Thumbnail Section */}
          <AnimatePresence>
            {currentIdea.status === 'thumbnail' && (
              <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -10 }} className="bg-white dark:bg-gray-800/50 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700/50">
                <h3 className="text-lg font-semibold flex items-center space-x-2 mb-4"><Image className="w-5 h-5 text-yellow-500" /><span>Thumbnail Description</span></h3>
                {isEditingThumbnail ? (
                  <div className="space-y-4">
                    <textarea value={manualThumbnail} onChange={(e) => setManualThumbnail(e.target.value)} placeholder="Describe your thumbnail..." rows={4} className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                    <div className="flex justify-end space-x-3">
                      <button onClick={() => setIsEditingThumbnail(false)} className="px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">Cancel</button>
                      <button onClick={handleSaveManualThumbnail} className="bg-blue-500 text-white px-4 py-2 rounded-lg">Save</button>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onClick={() => handleGenerate('thumbnail')} disabled={isGenerating} className="flex flex-col items-center justify-center p-6 bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors border border-gray-200 dark:border-gray-600">
                      <Sparkles className="w-8 h-8 text-yellow-500 mb-2" />
                      <span className="font-semibold">Generate with AI</span>
                      {isGenerating && generationStep === 'thumbnail' && <Loader2 className="w-4 h-4 animate-spin mt-2" />}
                    </button>
                    <button onClick={() => setIsEditingThumbnail(true)} className="flex flex-col items-center justify-center p-6 bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors border border-gray-200 dark:border-gray-600">
                      <Type className="w-8 h-8 text-blue-500 mb-2" />
                      <span className="font-semibold">Enter Manually</span>
                    </button>
                    <button onClick={() => handleUpdateStatus('script')} className="flex flex-col items-center justify-center p-6 bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors border border-gray-200 dark:border-gray-600">
                      <SkipForward className="w-8 h-8 text-gray-500 mb-2" />
                      <span className="font-semibold">Skip for Now</span>
                    </button>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Script Section */}
          <AnimatePresence>
            {(currentIdea.status === 'script' || currentIdea.status === 'done') && (
              <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} className="bg-white dark:bg-gray-800/50 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold flex items-center space-x-2"><Video className="w-5 h-5 text-purple-500" /><span>Video Script</span></h3>
                  {currentIdea.content.script ? (
                    <div className="flex items-center space-x-2">
                       <button onClick={() => navigator.clipboard.writeText(currentIdea.content.script || '')} className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"><Copy className="w-4 h-4" /></button>
                       <button onClick={() => handleGenerate('script')} disabled={isGenerating} className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm flex items-center space-x-2">
                         {isGenerating && generationStep === 'script' ? <Loader2 className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}<span>Regenerate</span>
                       </button>
                    </div>
                  ) : (
                    <button onClick={() => handleGenerate('script')} disabled={isGenerating} className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm flex items-center space-x-2">
                      {isGenerating && generationStep === 'script' ? <Loader2 className="w-4 h-4 animate-spin" /> : <Sparkles className="w-4 h-4" />}<span>Generate Script</span>
                    </button>
                  )}
                </div>
                {isGenerating && generationStep === 'script' && <p className="text-center text-gray-500 dark:text-gray-400 py-8">Generating script...</p>}
                {!isGenerating && !currentIdea.content.script && <div className="p-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-center text-gray-500 dark:text-gray-400">Generate a script based on your title.</div>}
                {currentIdea.content.script && (
                  <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg max-h-96 overflow-y-auto">
                    <pre className="whitespace-pre-wrap font-mono text-sm leading-relaxed">{currentIdea.content.script}</pre>
                  </div>
                )}
                {currentIdea.content.script && currentIdea.status !== 'done' && (
                  <div className="flex justify-center mt-6">
                    <button onClick={() => handleUpdateStatus('done')} className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-medium flex items-center space-x-2"><Check className="w-5 h-5" /><span>Mark as Complete</span></button>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default IdeaView;
