import React, { useEffect } from 'react';
import { Check<PERSON>ircle, XCircle, RefreshCw } from 'lucide-react';

const YouTubeCallback: React.FC = () => {
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const error = urlParams.get('error');

    if (code) {
      // Send success message to parent window
      if (window.opener) {
        window.opener.postMessage({
          type: 'YOUTUBE_AUTH_SUCCESS',
          code: code
        }, window.location.origin);
        window.close();
      }
    } else if (error) {
      // Send error message to parent window
      if (window.opener) {
        window.opener.postMessage({
          type: 'YOUTUBE_AUTH_ERROR',
          error: error
        }, window.location.origin);
        window.close();
      }
    }
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <RefreshCw className="w-16 h-16 animate-spin text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
          Processing Authentication
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Please wait while we complete the authentication process...
        </p>
      </div>
    </div>
  );
};

export default YouTubeCallback;
