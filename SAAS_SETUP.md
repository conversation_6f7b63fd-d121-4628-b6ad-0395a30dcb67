# 🚀 SaaS YouTube Integration Setup

This guide shows you how to set up YouTube integration for your SaaS app where **you (the developer) configure the API once**, and **users get a seamless one-click experience**.

## 🎯 What Users Will Experience

✅ **One-click "Connect YouTube" button**  
✅ **No API keys or technical setup required**  
✅ **Secure OAuth flow handled by your backend**  
✅ **Instant access to their YouTube channels**  

## 🔧 Developer Setup (One-Time)

### Step 1: Google Cloud Console Setup

1. **Go to [Google Cloud Console](https://console.cloud.google.com/)**
2. **Create a new project** or select existing one
3. **Enable YouTube Data API v3**:
   - Go to "APIs & Services" → "Library"
   - Search for "YouTube Data API v3"
   - Click "Enable"

4. **Create API Key**:
   - Go to "APIs & Services" → "Credentials"
   - Click "Create Credentials" → "API key"
   - Copy the API key

5. **Create OAuth 2.0 Client**:
   - Click "Create Credentials" → "OAuth client ID"
   - Configure OAuth consent screen if prompted:
     - Choose "External" user type
     - App name: `YouTube Empire Manager`
     - Add your email as developer contact
     - Add scopes: `../auth/youtube.readonly`, `../auth/youtube.upload`
   - Choose "Web application"
   - Add authorized redirect URIs:
     - `http://localhost:5174/auth/callback` (development)
     - `https://yourdomain.com/auth/callback` (production)
   - Copy Client ID and Client Secret

### Step 2: Configure Backend Environment

Update `server/.env` with your credentials:

```env
# Server Configuration
PORT=3001

# Google OAuth Configuration (KEEP THESE SECRET!)
GOOGLE_CLIENT_ID=your_actual_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_actual_client_secret

# YouTube API Key
YOUTUBE_API_KEY=your_actual_api_key

# CORS Origins
CORS_ORIGINS=http://localhost:5173,http://localhost:5174,http://localhost:3000
```

### Step 3: Configure Frontend Environment

Update `.env` with your **public** credentials:

```env
# YouTube API Configuration (Public - Safe to expose)
VITE_YOUTUBE_API_KEY=your_actual_api_key
VITE_GOOGLE_CLIENT_ID=your_actual_client_id.apps.googleusercontent.com
VITE_GOOGLE_REDIRECT_URI=http://localhost:5174/auth/callback

# Backend API URL
VITE_API_BASE_URL=http://localhost:3001
```

## 🚀 Running the Application

### Option 1: Run Both Frontend & Backend Together
```bash
npm run dev:full
```

### Option 2: Run Separately
```bash
# Terminal 1 - Backend
npm run dev:backend

# Terminal 2 - Frontend  
npm run dev
```

## 🔒 Security Features

✅ **Client Secret Never Exposed**: Kept secure on your backend  
✅ **Token Exchange via Backend**: All OAuth flows go through your server  
✅ **CORS Protection**: Only your domains can access the API  
✅ **Error Handling**: Proper error messages for users  

## 🌐 Production Deployment

### Backend Deployment (e.g., Railway, Render, Heroku)
1. Deploy the `server/` folder to your hosting platform
2. Set environment variables in your hosting dashboard
3. Update `CORS_ORIGINS` to include your production domain

### Frontend Deployment (e.g., Vercel, Netlify)
1. Update `VITE_API_BASE_URL` to your backend URL
2. Update `VITE_GOOGLE_REDIRECT_URI` to your production callback URL
3. Deploy your frontend

### Update Google Cloud Console
1. Add production redirect URI to OAuth client
2. Update authorized domains in OAuth consent screen

## 🎉 User Experience

Once set up, your users will:

1. **Click "Connect YouTube"** button in your app
2. **Authenticate with Google** in a popup window
3. **Grant permissions** to access their YouTube data
4. **Automatically return** to your app with full access
5. **Start managing** their YouTube channels immediately

**No technical knowledge required from users!**

## 🐛 Troubleshooting

### "API not configured" error
- Check that YouTube Data API v3 is enabled in Google Cloud Console
- Verify API key is correctly set in both frontend and backend `.env` files

### "Redirect URI mismatch" error  
- Ensure redirect URI in Google Cloud Console matches exactly
- Check both development and production URLs are added

### "Client ID not found" error
- Verify `GOOGLE_CLIENT_ID` is correctly set in both `.env` files
- Make sure the Client ID includes the full `.apps.googleusercontent.com` suffix

### Backend connection errors
- Ensure backend is running on the correct port (3001)
- Check `VITE_API_BASE_URL` points to the correct backend URL
- Verify CORS origins include your frontend URL
