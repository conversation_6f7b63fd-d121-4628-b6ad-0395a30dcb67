import { GenerationSettings } from '../types';

// Mock AI service - replace with actual OpenRouter.ai implementation
export class AIService {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateTitle(ideaName: string, settings: GenerationSettings): Promise<string[]> {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock response - replace with actual OpenRouter.ai call
    const titles = [
      `iPhone 16 Review: Is It REALLY Worth the Upgrade?`,
      `The TRUTH About the iPhone 16 After 1 Week of Use`,
      `iPhone 16 Unboxing & First Impressions! (You Won't Believe This)`,
      `Camera Test: iPhone 16 vs The World's Best Smartphones`,
      `DON'T Buy The iPhone 16 Until You Watch This Honest Review!`,
    ];
    
    return titles;
  }

  async generateThumbnailDescription(title: string, settings: GenerationSettings): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const descriptions = [
      `Split-screen image showing the new iPhone 16 next to an older model. Bold, yellow text overlay asks "WORTH IT?". A shocked face emoji is included. High contrast lighting with vibrant colors to make it pop.`,
      `Close-up of a hand holding the iPhone 16, showcasing its new design feature. A large red arrow points to the key element. The background is blurred to create depth. Bold white text with a subtle drop shadow for readability.`,
      `A person with a surprised expression looking at the iPhone 16 screen. The background is a clean, modern setup. Text overlay in a modern sans-serif font says "MIND-BLOWING!".`,
      `Dramatic "golden hour" lighting on the iPhone 16, making it look premium and sleek. The background is dark and moody. Professional photography style with a shallow depth of field.`,
    ];
    
    return descriptions[Math.floor(Math.random() * descriptions.length)];
  }

  async generateScript(title: string, settings: GenerationSettings): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const scriptLength = settings.contentLength === 'short' ? 'short-form' : 'long-form';
    
    const scripts = {
      'short': `🎬 HOOK (0-3 seconds)
"They said the iPhone 16 was just a small update... they were WRONG. Here's why."

🎯 MAIN CONTENT (3-50 seconds)
[Quick transition with upbeat, techy music]

First, the camera. It's not what you think. [Show a stunning photo or video clip].

Then there's the battery life. I used it all day and... [Show battery percentage].

But the biggest change is this one feature nobody is talking about... [Showcase the unique feature].

📢 CALL TO ACTION (50-60 seconds)
Is the iPhone 16 a must-have? Let me know your thoughts in the comments! Follow for more honest tech reviews!

#iphone16 #techreview #apple`,

      'long': `🎬 INTRO (0-30 seconds)
What's up everyone, and welcome back! Today, we're doing a deep-dive review of the brand new iPhone 16. I've spent the last 7 days putting it through its paces to find out if it's truly worth your hard-earned money.

We'll cover design, performance, the new camera system, and that controversial new feature. By the end of this video, you'll know if you should upgrade.

Let's get into it.

🎯 SECTION 1: Design & Display (30 seconds - 3 minutes)
First impressions matter. Let's talk about what's new with the design... [Detailed explanation with B-roll footage].

🎯 SECTION 2: Performance & Battery (3-6 minutes)
Apple claims this is the fastest chip in a smartphone. Is it? Let's run some real-world tests... [Show speed tests, gaming, etc.]. And what about battery life?

🎯 SECTION 3: The Camera System (6-8 minutes)
This is the moment you've been waiting for. Let's look at some photo and video samples... [Show comparisons, low light shots, zoom capabilities].

🎯 SECTION 4: The Verdict (8-10 minutes)
So, the big question: should you buy the iPhone 16? Here's my final verdict...

📢 OUTRO (10-11 minutes)
That's a wrap on my iPhone 16 review! If this video helped you make a decision, a thumbs up would be amazing.

What's your favorite new feature? Drop it in the comments below! Subscribe for more in-depth reviews. Until next time!`,
    };
    
    return scripts[settings.contentLength === 'short' ? 'short' : 'long'];
  }
}

export const createPrompt = (type: 'title' | 'thumbnail' | 'script', context: any, settings: GenerationSettings): string => {
  const basePrompts = {
    title: `Generate 5 distinct and compelling YouTube video titles for an idea about: "${context.ideaName}". 
    Tone: ${settings.tone}
    Target Audience: ${settings.targetAudience}
    Language: ${settings.language}
    Make them engaging, clickable, and SEO-friendly.`,
    
    thumbnail: `Create a detailed description for a YouTube thumbnail image for the video titled: "${context.title}".
    Tone: ${settings.tone}
    Target Audience: ${settings.targetAudience}
    Describe colors, composition, text overlay, and visual elements that would make it stand out and get clicks.`,
    
    script: `Write a ${settings.contentLength}-form YouTube video script for: "${context.title}".
    Tone: ${settings.tone}
    Target Audience: ${settings.targetAudience}
    Language: ${settings.language}
    Include timestamps, hooks, transitions, and clear calls-to-action.`,
  };
  
  return basePrompts[type];
};
