import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Channel, Folder, Idea, GenerationPreset } from '../types';
import { storage } from '../utils/storage';
import { dataLoader } from '../utils/dataLoader';

interface AppState {
  channels: Channel[];
  folders: Folder[];
  ideas: Idea[];
  presets: GenerationPreset[];
  selectedChannelId: string | null;
  selectedFolderId: string | null;
  selectedIdeaId: string | null;
  darkMode: boolean;
  apiKey: string;
  isAddChannelModalOpen: boolean;
}

type AppAction =
  | { type: 'SET_CHANNELS'; payload: Channel[] }
  | { type: 'ADD_CHANNEL'; payload: Channel }
  | { type: 'UPDATE_CHANNEL'; payload: Channel }
  | { type: 'DELETE_CHANNEL'; payload: string }
  | { type: 'SET_FOLDERS'; payload: Folder[] }
  | { type: 'ADD_FOLDER'; payload: Folder }
  | { type: 'UPDATE_FOLDER'; payload: Folder }
  | { type: 'DELETE_FOLDER'; payload: string }
  | { type: 'SET_IDEAS'; payload: Idea[] }
  | { type: 'ADD_IDEA'; payload: Idea }
  | { type: 'UPDATE_IDEA'; payload: Idea }
  | { type: 'DELETE_IDEA'; payload: string }
  | { type: 'SET_PRESETS'; payload: GenerationPreset[] }
  | { type: 'ADD_PRESET'; payload: GenerationPreset }
  | { type: 'DELETE_PRESET'; payload: string }
  | { type: 'SELECT_CHANNEL'; payload: string | null }
  | { type: 'SELECT_FOLDER'; payload: string | null }
  | { type: 'SELECT_IDEA'; payload: string | null }
  | { type: 'TOGGLE_DARK_MODE' }
  | { type: 'SET_API_KEY'; payload: string }
  | { type: 'LOAD_DATA' }
  | { type: 'OPEN_ADD_CHANNEL_MODAL' }
  | { type: 'CLOSE_ADD_CHANNEL_MODAL' };

const initialState: AppState = {
  channels: [],
  folders: [],
  ideas: [],
  presets: [],
  selectedChannelId: null,
  selectedFolderId: null,
  selectedIdeaId: null,
  darkMode: false,
  apiKey: '',
  isAddChannelModalOpen: false,
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_CHANNELS':
      return { ...state, channels: action.payload };

    case 'ADD_CHANNEL': {
      const newChannels = [...state.channels, action.payload];
      return { ...state, channels: newChannels };
    }

    case 'UPDATE_CHANNEL': {
      const updatedChannels = state.channels.map(ch => 
        ch.id === action.payload.id ? action.payload : ch
      );
      return { ...state, channels: updatedChannels };
    }

    case 'DELETE_CHANNEL': {
      const filteredChannels = state.channels.filter(ch => ch.id !== action.payload);
      const filteredFolders = state.folders.filter(f => f.channelId !== action.payload);
      const filteredIdeas = state.ideas.filter(i => i.channelId !== action.payload);
      return { 
        ...state, 
        channels: filteredChannels,
        folders: filteredFolders,
        ideas: filteredIdeas,
        selectedChannelId: state.selectedChannelId === action.payload ? null : state.selectedChannelId
      };
    }

    case 'SET_FOLDERS':
      return { ...state, folders: action.payload };

    case 'ADD_FOLDER': {
      const newFolders = [...state.folders, action.payload];
      return { ...state, folders: newFolders };
    }

    case 'UPDATE_FOLDER': {
      const updatedFolders = state.folders.map(f => 
        f.id === action.payload.id ? action.payload : f
      );
      return { ...state, folders: updatedFolders };
    }

    case 'DELETE_FOLDER': {
      const remainingFolders = state.folders.filter(f => f.id !== action.payload);
      const remainingIdeas = state.ideas.filter(i => i.folderId !== action.payload);
      return { 
        ...state, 
        folders: remainingFolders,
        ideas: remainingIdeas,
        selectedFolderId: state.selectedFolderId === action.payload ? null : state.selectedFolderId
      };
    }

    case 'SET_IDEAS':
      storage.saveIdeas(action.payload);
      return { ...state, ideas: action.payload };

    case 'ADD_IDEA':
      const newIdeas = [...state.ideas, action.payload];
      storage.saveIdeas(newIdeas);
      return { ...state, ideas: newIdeas };

    case 'UPDATE_IDEA':
      const updatedIdeas = state.ideas.map(i => 
        i.id === action.payload.id ? action.payload : i
      );
      storage.saveIdeas(updatedIdeas);
      return { ...state, ideas: updatedIdeas };

    case 'DELETE_IDEA':
      const remainingIdeasAfterDelete = state.ideas.filter(i => i.id !== action.payload);
      storage.saveIdeas(remainingIdeasAfterDelete);
      return { 
        ...state, 
        ideas: remainingIdeasAfterDelete,
        selectedIdeaId: state.selectedIdeaId === action.payload ? null : state.selectedIdeaId
      };

    case 'SET_PRESETS':
      storage.savePresets(action.payload);
      return { ...state, presets: action.payload };

    case 'ADD_PRESET':
      const newPresets = [...state.presets, action.payload];
      storage.savePresets(newPresets);
      return { ...state, presets: newPresets };

    case 'DELETE_PRESET':
      const remainingPresets = state.presets.filter(p => p.id !== action.payload);
      storage.savePresets(remainingPresets);
      return { ...state, presets: remainingPresets };

    case 'SELECT_CHANNEL':
      return { ...state, selectedChannelId: action.payload, selectedFolderId: null, selectedIdeaId: null };

    case 'SELECT_FOLDER':
      return { ...state, selectedFolderId: action.payload, selectedIdeaId: null };

    case 'SELECT_IDEA':
      return { ...state, selectedIdeaId: action.payload };

    case 'TOGGLE_DARK_MODE':
      const newDarkMode = !state.darkMode;
      storage.saveSettings({ ...storage.getSettings(), darkMode: newDarkMode });
      return { ...state, darkMode: newDarkMode };

    case 'SET_API_KEY':
      storage.saveSettings({ ...storage.getSettings(), apiKey: action.payload });
      return { ...state, apiKey: action.payload };
    
    case 'OPEN_ADD_CHANNEL_MODAL':
      return { ...state, isAddChannelModalOpen: true };
    
    case 'CLOSE_ADD_CHANNEL_MODAL':
      return { ...state, isAddChannelModalOpen: false };

    default:
      return state;
  }
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  useEffect(() => {
    const loadData = async () => {
      const { channels, folders, ideas } = await dataLoader.loadAllData();
      const settings = storage.getSettings();
      const presets = storage.getPresets();
      
      dispatch({ type: 'SET_CHANNELS', payload: channels });
      dispatch({ type: 'SET_FOLDERS', payload: folders });
      dispatch({ type: 'SET_IDEAS', payload: ideas });
      dispatch({ type: 'SET_PRESETS', payload: presets });
      dispatch({ type: 'SET_API_KEY', payload: settings.apiKey });
      if (settings.darkMode) {
        dispatch({ type: 'TOGGLE_DARK_MODE' });
      }
    };
    
    loadData();
  }, []);

  useEffect(() => {
    if (state.darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [state.darkMode]);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
};
