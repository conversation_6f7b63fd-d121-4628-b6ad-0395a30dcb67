import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Settings, 
  Folder, 
  Lightbulb, 
  Calendar,
  Users,
  TrendingUp,
  Edit,
  Trash2
} from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import { useApp } from '../../contexts/AppContext';
import { dataLoader } from '../../utils/dataLoader';

const ChannelView: React.FC = () => {
  const { state, dispatch } = useApp();
  const [showNewFolderForm, setShowNewFolderForm] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');

  const currentChannel = state.channels.find(ch => ch.id === state.selectedChannelId);
  const channelFolders = state.folders.filter(f => f.channelId === state.selectedChannelId);
  const channelIdeas = state.ideas.filter(i => i.channelId === state.selectedChannelId);

  if (!currentChannel) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-gray-500 dark:text-gray-400">Channel not found</p>
      </div>
    );
  }

  const handleCreateFolder = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newFolderName.trim()) return;

    const newFolder = {
      id: uuidv4(),
      channelId: currentChannel.id,
      name: newFolderName,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Optimistic update
    dispatch({ type: 'ADD_FOLDER', payload: newFolder });

    // Save to Supabase
    const result = await dataLoader.createFolder(newFolder);
    
    if (!result) {
      // Rollback on error
      dispatch({ type: 'DELETE_FOLDER', payload: newFolder.id });
      alert('Failed to create folder. Please try again.');
      return;
    }

    setNewFolderName('');
    setShowNewFolderForm(false);
  };

  const getFolderStats = (folderId: string) => {
    const folderIdeas = state.ideas.filter(i => i.folderId === folderId);
    const completed = folderIdeas.filter(i => i.status === 'done').length;
    const total = folderIdeas.length;
    return { completed, total, percentage: total > 0 ? Math.round((completed / total) * 100) : 0 };
  };

  const getChannelStats = () => {
    const totalIdeas = channelIdeas.length;
    const completedIdeas = channelIdeas.filter(i => i.status === 'done').length;
    const inProgressIdeas = channelIdeas.filter(i => i.status !== 'idea' && i.status !== 'done').length;
    
    return {
      totalFolders: channelFolders.length,
      totalIdeas,
      completedIdeas,
      inProgressIdeas,
      completionRate: totalIdeas > 0 ? Math.round((completedIdeas / totalIdeas) * 100) : 0
    };
  };

  const stats = getChannelStats();

  return (
    <div className="h-full overflow-y-auto">
      <div className="max-w-6xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div
              className="w-12 h-12 rounded-xl flex items-center justify-center text-white font-bold text-lg"
              style={{ backgroundColor: currentChannel.color }}
            >
              {currentChannel.name.charAt(0).toUpperCase()}
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {currentChannel.name}
              </h1>
              {currentChannel.description && (
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  {currentChannel.description}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
              <Edit className="w-5 h-5" />
            </button>
            <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Folders</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalFolders}</p>
              </div>
              <Folder className="w-8 h-8 text-blue-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Ideas</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalIdeas}</p>
              </div>
              <Lightbulb className="w-8 h-8 text-yellow-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">In Progress</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.inProgressIdeas}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.completedIdeas}</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-green-600 dark:text-green-400 font-medium">
                  {stats.completionRate}%
                </p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Folders Grid */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Content Folders
          </h2>
          <button
            onClick={() => setShowNewFolderForm(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>New Folder</span>
          </button>
        </div>

        {/* New Folder Form */}
        {showNewFolderForm && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-6"
          >
            <form onSubmit={handleCreateFolder} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Folder Name
                </label>
                <input
                  type="text"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  placeholder="e.g., Tech Reviews, Tutorials, Shorts..."
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  autoFocus
                />
              </div>
              <div className="flex items-center space-x-3">
                <button
                  type="submit"
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Create Folder
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowNewFolderForm(false);
                    setNewFolderName('');
                  }}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </motion.div>
        )}

        {/* Folders */}
        {channelFolders.length === 0 ? (
          <div className="text-center py-12">
            <Folder className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No folders yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Create your first folder to organize your video ideas
            </p>
            <button
              onClick={() => setShowNewFolderForm(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2 mx-auto"
            >
              <Plus className="w-5 h-5" />
              <span>Create First Folder</span>
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {channelFolders.map((folder, index) => {
              const folderStats = getFolderStats(folder.id);
              
              return (
                <motion.div
                  key={folder.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => dispatch({ type: 'SELECT_FOLDER', payload: folder.id })}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <Folder className="w-8 h-8 text-blue-500" />
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {folder.name}
                      </h3>
                    </div>
                    <button className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                      <Settings className="w-4 h-4" />
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Ideas</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {folderStats.completed}/{folderStats.total}
                      </span>
                    </div>
                    
                    {folderStats.total > 0 && (
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${folderStats.percentage}%` }}
                        />
                      </div>
                    )}
                    
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Created {new Date(folder.createdAt).toLocaleDateString()}
                      </span>
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                        {folderStats.percentage}% complete
                      </span>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChannelView;
