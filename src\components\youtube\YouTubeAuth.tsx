import React, { useState, useEffect } from 'react';
import { Youtube, ExternalLink, AlertCircle, CheckCircle } from 'lucide-react';
import { youtubeApi } from '../../services/youtubeApi';

interface YouTubeAuthProps {
  onAuthSuccess: (tokens: any) => void;
  onAuthError: (error: string) => void;
}

const YouTubeAuth: React.FC<YouTubeAuthProps> = ({ onAuthSuccess, onAuthError }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [authUrl, setAuthUrl] = useState<string>('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check if already authenticated
    setIsAuthenticated(youtubeApi.isAuthenticated());
    
    // Generate auth URL
    try {
      const url = youtubeApi.getAuthUrl();
      setAuthUrl(url);
    } catch (error) {
      console.error('Error generating auth URL:', error);
      onAuthError('Failed to generate authentication URL. Please check your Google API configuration.');
    }
  }, [onAuthError]);

  const handleAuthClick = () => {
    if (authUrl) {
      window.open(authUrl, '_blank', 'width=500,height=600');
      setIsLoading(true);
      
      // Listen for the auth callback
      const handleMessage = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;
        
        if (event.data.type === 'YOUTUBE_AUTH_SUCCESS') {
          handleAuthCode(event.data.code);
          window.removeEventListener('message', handleMessage);
        } else if (event.data.type === 'YOUTUBE_AUTH_ERROR') {
          setIsLoading(false);
          onAuthError(event.data.error);
          window.removeEventListener('message', handleMessage);
        }
      };

      window.addEventListener('message', handleMessage);
      
      // Cleanup after 5 minutes
      setTimeout(() => {
        window.removeEventListener('message', handleMessage);
        setIsLoading(false);
      }, 300000);
    }
  };

  const handleAuthCode = async (code: string) => {
    try {
      const tokens = await youtubeApi.getTokens(code);
      setIsAuthenticated(true);
      setIsLoading(false);
      onAuthSuccess(tokens);
    } catch (error) {
      console.error('Error exchanging code for tokens:', error);
      setIsLoading(false);
      onAuthError('Failed to authenticate with YouTube. Please try again.');
    }
  };

  if (isAuthenticated) {
    return (
      <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
        <CheckCircle className="w-5 h-5" />
        <span>Connected to YouTube</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
        <Youtube className="w-5 h-5" />
        <span>Connect your YouTube account to manage channels</span>
      </div>
      
      {!authUrl ? (
        <div className="flex items-center space-x-2 text-amber-600 dark:text-amber-400">
          <AlertCircle className="w-5 h-5" />
          <span>YouTube integration is being set up. Please contact support if this persists.</span>
        </div>
      ) : (
        <button
          onClick={handleAuthClick}
          disabled={isLoading}
          className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg disabled:bg-red-400 transition-colors"
        >
          <Youtube className="w-5 h-5" />
          <span>{isLoading ? 'Authenticating...' : 'Connect YouTube Account'}</span>
          <ExternalLink className="w-4 h-4" />
        </button>
      )}
      
      <div className="text-sm text-gray-500 dark:text-gray-400">
        <p>This will open a new window to authenticate with Google.</p>
        <p>Make sure to allow popups for this site.</p>
      </div>
    </div>
  );
};

export default YouTubeAuth;
