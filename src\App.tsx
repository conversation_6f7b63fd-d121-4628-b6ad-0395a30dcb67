import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AppProvider, useApp } from './contexts/AppContext';
import Sidebar from './components/Sidebar';
import MainContent from './components/MainContent';
import AddChannelModal from './components/modals/AddChannelModal';
import YouTubeCallback from './components/youtube/YouTubeCallback';
import { Channel } from './types';
import { supabase } from './utils/supabase';
import Auth from './components/Auth';
import { Session } from '@supabase/supabase-js';
import { dataLoader } from './utils/dataLoader';

function AppContent() {
  const { state, dispatch } = useApp();

  const handleAddChannel = async (channel: Channel) => {
    // Optimistic update
    dispatch({ type: 'ADD_CHANNEL', payload: channel });
    dispatch({ type: 'SELECT_CHANNEL', payload: channel.id });

    // Save to Supabase
    const result = await dataLoader.createChannel(channel);
    
    if (!result) {
      // Rollback on error
      dispatch({ type: 'DELETE_CHANNEL', payload: channel.id });
      alert('Failed to create channel. Please try again.');
    }
  };

  return (
    <Router>
      <Routes>
        <Route path="/auth/callback" element={<YouTubeCallback />} />
        <Route path="/*" element={
          <>
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
              <Sidebar />
              <MainContent />
            </div>
            <AddChannelModal
              isOpen={state.isAddChannelModalOpen}
              onClose={() => dispatch({ type: 'CLOSE_ADD_CHANNEL_MODAL' })}
              onAddChannel={handleAddChannel}
            />
          </>
        } />
      </Routes>
    </Router>
  );
}

function App() {
  const [session, setSession] = useState<Session | null>(null);

  useEffect(() => {
    // TEMPORARY: Skip authentication for testing
    setSession({ user: { id: 'test-user' } } as any);
    
    // supabase.auth.getSession().then(({ data: { session } }) => {
    //   setSession(session);
    // });

    // const {
    //   data: { subscription },
    // } = supabase.auth.onAuthStateChange((_event, session) => {
    //   setSession(session);
    // });

    // return () => subscription.unsubscribe();
  }, []);

  return (
    <AppProvider>
      {!session ? <Auth /> : <AppContent key={session.user.id} />}
    </AppProvider>
  );
}

export default App;
