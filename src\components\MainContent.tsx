import React from 'react';
import { useApp } from '../contexts/AppContext';
import ChannelView from './views/ChannelView';
import FolderView from './views/FolderView';
import IdeaView from './views/IdeaView';
import DashboardView from './views/DashboardView';

const MainContent: React.FC = () => {
  const { state } = useApp();

  const renderContent = () => {
    if (state.selectedIdeaId) {
      return <IdeaView />;
    }
    
    if (state.selectedFolderId) {
      return <FolderView />;
    }
    
    if (state.selectedChannelId) {
      return <ChannelView />;
    }
    
    return <DashboardView />;
  };

  return (
    <div className="flex-1 bg-gray-50 dark:bg-gray-900 overflow-hidden">
      {renderContent()}
    </div>
  );
};

export default MainContent;
