export interface Channel {
  id: string;
  name: string;
  description?: string;
  color: string;
  defaultSettings: GenerationSettings;
  createdAt: string;
  updatedAt: string;
}

export interface Folder {
  id: string;
  channelId: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Idea {
  id: string;
  folderId: string;
  channelId: string;
  name: string;
  status: 'idea' | 'title' | 'thumbnail' | 'script' | 'done';
  tags: string[];
  priority: 'low' | 'medium' | 'high';
  content: {
    generatedTitles?: string[];
    title?: string;
    thumbnailDescription?: string;
    script?: string;
  };
  promptHistory: PromptHistoryEntry[];
  createdAt: string;
  updatedAt: string;
}

export interface GenerationSettings {
  model: string;
  temperature: number;
  tone: string;
  contentLength: 'short' | 'medium' | 'long';
  targetAudience: string;
  language: string;
}

export interface PromptHistoryEntry {
  id: string;
  step: 'title' | 'thumbnail' | 'script';
  prompt: string;
  response: string;
  model: string;
  timestamp: string;
}

export interface GenerationPreset {
  id: string;
  name: string;
  settings: GenerationSettings;
  channelId: string;
}
