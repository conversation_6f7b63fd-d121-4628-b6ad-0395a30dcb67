import React, { useState } from 'react';
import YouTubeIntegrationModal from './modals/YouTubeIntegrationModal';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronDown, 
  ChevronRight, 
  Folder, 
  FolderOpen, 
  Lightbulb, 
  Plus, 
  Youtube, 
  Settings,
  Moon,
  Sun,
  Search,
  Edit
} from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { Idea } from '../types';
import { supabase } from '../utils/supabase';

const Sidebar: React.FC = () => {
  const { state, dispatch } = useApp();
  const [expandedChannels, setExpandedChannels] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [editingFolderId, setEditingFolderId] = useState<string | null>(null);
  const [editingFolderName, setEditingFolderName] = useState('');
  const [isYouTubeModalOpen, setIsYouTubeModalOpen] = useState(false);

  const handleUpdateFolder = async (folderId: string) => {
    const folder = state.folders.find(f => f.id === folderId);
    if (!folder || folder.name === editingFolderName) {
      setEditingFolderId(null);
      return;
    }

    const updatedFolder = { ...folder, name: editingFolderName, updatedAt: new Date().toISOString() };

    dispatch({ type: 'UPDATE_FOLDER', payload: updatedFolder });

    const { error } = await supabase
      .from('youtubetool_folders')
      .update({ name: editingFolderName, updated_at: updatedFolder.updatedAt })
      .eq('id', folderId);

    if (error) {
      console.error('Error updating folder:', error);
      dispatch({ type: 'UPDATE_FOLDER', payload: folder }); // Revert state
      alert('Failed to update folder name.');
    }

    setEditingFolderId(null);
  };

  const toggleChannel = (channelId: string) => {
    const newExpanded = new Set(expandedChannels);
    if (newExpanded.has(channelId)) {
      newExpanded.delete(channelId);
    } else {
      newExpanded.add(channelId);
    }
    setExpandedChannels(newExpanded);
  };

  const getChannelFolders = (channelId: string) => {
    return state.folders.filter(folder => folder.channelId === channelId);
  };

  const getFolderIdeas = (folderId: string) => {
    return state.ideas.filter(idea => idea.folderId === folderId);
  };

  const getIdeaStatusColor = (status: Idea['status']) => {
    switch (status) {
      case 'idea': return 'text-gray-400';
      case 'title': return 'text-blue-400';
      case 'thumbnail': return 'text-yellow-400';
      case 'script': return 'text-purple-400';
      case 'done': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getPriorityColor = (priority: Idea['priority']) => {
    switch (priority) {
      case 'high': return 'border-l-red-500';
      case 'medium': return 'border-l-yellow-500';
      case 'low': return 'border-l-green-500';
      default: return 'border-l-gray-500';
    }
  };

  const filteredChannels = state.channels.filter(channel =>
    channel.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="w-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Youtube className="w-6 h-6 text-red-500" />
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              Empire Manager
            </h1>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => dispatch({ type: 'TOGGLE_DARK_MODE' })}
              className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              {state.darkMode ? (
                <Sun className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              ) : (
                <Moon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              )}
            </button>
            <button className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <Settings className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search channels..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Channel List */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-2">
          {filteredChannels.map((channel) => (
            <div key={channel.id} className="space-y-1">
              {/* Channel */}
              <div
                className={`flex items-center space-x-2 p-2 rounded-lg cursor-pointer transition-colors ${
                  state.selectedChannelId === channel.id
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
                }`}
                onClick={() => {
                  dispatch({ type: 'SELECT_CHANNEL', payload: channel.id });
                  toggleChannel(channel.id);
                }}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleChannel(channel.id);
                  }}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                >
                  {expandedChannels.has(channel.id) ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </button>
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: channel.color }}
                />
                <span className="text-sm font-medium truncate flex-1">
                  {channel.name}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {getChannelFolders(channel.id).length}
                </span>
              </div>

              {/* Folders */}
              <AnimatePresence>
                {expandedChannels.has(channel.id) && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="ml-6 space-y-1"
                  >
                    {getChannelFolders(channel.id).map((folder) => (
                      <div key={folder.id} className="space-y-1">
                        {/* Folder */}
                                                <div
                          className={`group flex items-center space-x-2 p-2 rounded-lg cursor-pointer transition-colors ${
                            state.selectedFolderId === folder.id
                              ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                              : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400'
                          }`}
                          onClick={() => dispatch({ type: 'SELECT_FOLDER', payload: folder.id })}
                        >
                          {state.selectedFolderId === folder.id ? (
                            <FolderOpen className="w-4 h-4" />
                          ) : (
                            <Folder className="w-4 h-4" />
                          )}
                          {editingFolderId === folder.id ? (
                            <input
                              type="text"
                              value={editingFolderName}
                              onChange={(e) => setEditingFolderName(e.target.value)}
                              onBlur={() => handleUpdateFolder(folder.id)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleUpdateFolder(folder.id);
                                }
                              }}
                              className="text-sm bg-transparent border-b border-blue-500 focus:outline-none flex-1"
                              autoFocus
                            />
                          ) : (
                            <span className="text-sm truncate flex-1">
                              {folder.name}
                            </span>
                          )}
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {getFolderIdeas(folder.id).length}
                          </span>
                          <button onClick={(e) => {
                            e.stopPropagation();
                            setEditingFolderId(folder.id);
                            setEditingFolderName(folder.name);
                          }} className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <Edit className="w-3 h-3" />
                          </button>
                        </div>

                        {/* Ideas */}
                        {state.selectedFolderId === folder.id && (
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="ml-6 space-y-1"
                          >
                            {getFolderIdeas(folder.id).map((idea) => (
                              <div
                                key={idea.id}
                                className={`flex items-center space-x-2 p-2 rounded-lg cursor-pointer transition-colors border-l-2 ${getPriorityColor(idea.priority)} ${
                                  state.selectedIdeaId === idea.id
                                    ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                                    : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400'
                                }`}
                                onClick={() => dispatch({ type: 'SELECT_IDEA', payload: idea.id })}
                              >
                                <Lightbulb className={`w-4 h-4 ${getIdeaStatusColor(idea.status)}`} />
                                <span className="text-sm truncate flex-1">
                                  {idea.name}
                                </span>
                                {idea.tags.length > 0 && (
                                  <span className="text-xs bg-gray-200 dark:bg-gray-700 px-1.5 py-0.5 rounded">
                                    {idea.tags.length}
                                  </span>
                                )}
                              </div>
                            ))}
                          </motion.div>
                        )}
                      </div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </div>

        {/* Add Channel Button */}
        <button
          onClick={() => dispatch({ type: 'OPEN_ADD_CHANNEL_MODAL' })}
          className="w-full mt-4 p-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors flex items-center justify-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span className="text-sm">Add Channel</span>
        </button>

        {/* YouTube Integration Button */}
        <button
          onClick={() => setIsYouTubeModalOpen(true)}
          className="w-full mt-2 p-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center justify-center space-x-2"
        >
          <Youtube className="w-4 h-4" />
          <span className="text-sm">Connect YouTube</span>
        </button>
      </div>
      
      <YouTubeIntegrationModal 
        isOpen={isYouTubeModalOpen}
        onClose={() => setIsYouTubeModalOpen(false)}
      />
    </div>
  );
};

export default Sidebar;
