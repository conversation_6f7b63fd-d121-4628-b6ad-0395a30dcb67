import React, { useState, useEffect } from 'react';
import { 
  Youtube, 
  Users, 
  Video, 
  Eye, 
  Download, 
  RefreshCw, 
  ExternalLink,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { youtubeApi, YouTubeChannel } from '../../services/youtubeApi';
import { useApp } from '../../contexts/AppContext';
import { dataLoader } from '../../utils/dataLoader';
import { v4 as uuidv4 } from 'uuid';

const YouTubeChannelImport: React.FC = () => {
  const { dispatch } = useApp();
  const [channels, setChannels] = useState<YouTubeChannel[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [importingChannels, setImportingChannels] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (youtubeApi.isAuthenticated()) {
      loadChannels();
    }
  }, []);

  const loadChannels = async () => {
    setLoading(true);
    setError('');
    
    try {
      const ytChannels = await youtubeApi.getMyChannels();
      setChannels(ytChannels);
    } catch (err) {
      console.error('Error loading channels:', err);
      setError('Failed to load YouTube channels. Please try reconnecting your account.');
    } finally {
      setLoading(false);
    }
  };

  const importChannel = async (ytChannel: YouTubeChannel) => {
    setImportingChannels(prev => new Set([...prev, ytChannel.id]));
    
    try {
      // Create channel in our app
      const newChannel = {
        id: uuidv4(),
        name: ytChannel.title,
        description: ytChannel.description,
        color: '#FF0000', // YouTube red
        defaultSettings: {
          youtubeChannelId: ytChannel.id,
          youtubeChannelTitle: ytChannel.title,
          subscriberCount: ytChannel.subscriberCount,
          videoCount: ytChannel.videoCount,
          viewCount: ytChannel.viewCount,
          customUrl: ytChannel.customUrl,
          thumbnails: ytChannel.thumbnails,
          isYouTubeChannel: true,
          lastSynced: new Date().toISOString()
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Save to database
      const result = await dataLoader.createChannel(newChannel);
      
      if (result) {
        // Add to app state
        dispatch({ type: 'ADD_CHANNEL', payload: newChannel });
        dispatch({ type: 'SELECT_CHANNEL', payload: newChannel.id });
        
        // Optionally import some recent videos as ideas
        await importRecentVideosAsIdeas(ytChannel.id, newChannel.id);
      } else {
        throw new Error('Failed to save channel to database');
      }
    } catch (err) {
      console.error('Error importing channel:', err);
      setError(`Failed to import channel "${ytChannel.title}". Please try again.`);
    } finally {
      setImportingChannels(prev => {
        const newSet = new Set(prev);
        newSet.delete(ytChannel.id);
        return newSet;
      });
    }
  };

  const importRecentVideosAsIdeas = async (ytChannelId: string, appChannelId: string) => {
    try {
      const videos = await youtubeApi.getChannelVideos(ytChannelId, 10); // Get last 10 videos
      
      // Create a "Published Videos" folder
      const folder = {
        id: uuidv4(),
        name: 'Published Videos',
        channelId: appChannelId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const folderResult = await dataLoader.createFolder(folder);
      
      if (folderResult) {
        dispatch({ type: 'ADD_FOLDER', payload: folder });

        // Import videos as ideas
        for (const video of videos) {
          const idea = {
            id: uuidv4(),
            name: video.title,
            channelId: appChannelId,
            folderId: folder.id,
            status: 'published' as const,
            priority: 'medium' as const,
            content: {
              description: video.description,
              youtubeVideoId: video.id,
              thumbnails: video.thumbnails,
              publishedAt: video.publishedAt,
              viewCount: video.viewCount,
              likeCount: video.likeCount,
              commentCount: video.commentCount,
              duration: video.duration,
              tags: video.tags,
              isYouTubeVideo: true
            },
            tags: video.tags,
            promptHistory: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          const ideaResult = await dataLoader.createIdea(idea);
          if (ideaResult) {
            dispatch({ type: 'ADD_IDEA', payload: idea });
          }
        }
      }
    } catch (err) {
      console.error('Error importing videos as ideas:', err);
      // Don't throw - this is optional
    }
  };

  const formatNumber = (num: string) => {
    const number = parseInt(num);
    if (number >= 1000000) {
      return (number / 1000000).toFixed(1) + 'M';
    } else if (number >= 1000) {
      return (number / 1000).toFixed(1) + 'K';
    }
    return number.toString();
  };

  if (!youtubeApi.isAuthenticated()) {
    return (
      <div className="text-center py-8">
        <Youtube className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600 dark:text-gray-400">
          Please authenticate with YouTube first to import your channels.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Your YouTube Channels
        </h3>
        <button
          onClick={loadChannels}
          disabled={loading}
          className="flex items-center space-x-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg disabled:opacity-50 transition-colors"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {error && (
        <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-700 dark:text-red-400">
          <AlertCircle className="w-5 h-5" />
          <span>{error}</span>
        </div>
      )}

      {loading ? (
        <div className="text-center py-8">
          <RefreshCw className="w-8 h-8 animate-spin text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading your YouTube channels...</p>
        </div>
      ) : channels.length === 0 ? (
        <div className="text-center py-8">
          <Youtube className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">
            No YouTube channels found for your account.
          </p>
        </div>
      ) : (
        <div className="grid gap-4">
          {channels.map((channel) => {
            const isImporting = importingChannels.has(channel.id);
            
            return (
              <div
                key={channel.id}
                className="p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm"
              >
                <div className="flex items-start space-x-4">
                  <img
                    src={channel.thumbnails.medium.url}
                    alt={channel.title}
                    className="w-16 h-16 rounded-full"
                  />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                        {channel.title}
                      </h4>
                      <a
                        href={`https://youtube.com/channel/${channel.id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </div>
                    
                    {channel.customUrl && (
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        @{channel.customUrl}
                      </p>
                    )}
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                      {channel.description || 'No description available'}
                    </p>
                    
                    <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Users className="w-4 h-4" />
                        <span>{formatNumber(channel.subscriberCount)} subscribers</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Video className="w-4 h-4" />
                        <span>{formatNumber(channel.videoCount)} videos</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="w-4 h-4" />
                        <span>{formatNumber(channel.viewCount)} views</span>
                      </div>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => importChannel(channel)}
                    disabled={isImporting}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:bg-blue-400 transition-colors"
                  >
                    {isImporting ? (
                      <>
                        <RefreshCw className="w-4 h-4 animate-spin" />
                        <span>Importing...</span>
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4" />
                        <span>Import</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default YouTubeChannelImport;
