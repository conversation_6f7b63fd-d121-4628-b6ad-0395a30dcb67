import React from 'react';
import { motion } from 'framer-motion';
import { 
  Youtube, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  Plus,
  BarChart3,
  Calendar,
  Star
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';

const DashboardView: React.FC = () => {
  const { state, dispatch } = useApp();

  const getStats = () => {
    const totalChannels = state.channels.length;
    const totalFolders = state.folders.length;
    const totalIdeas = state.ideas.length;
    const completedIdeas = state.ideas.filter(idea => idea.status === 'done').length;
    const inProgressIdeas = state.ideas.filter(idea => idea.status !== 'idea' && idea.status !== 'done').length;
    const highPriorityIdeas = state.ideas.filter(idea => idea.priority === 'high').length;

    return {
      totalChannels,
      totalFolders,
      totalIdeas,
      completedIdeas,
      inProgressIdeas,
      highPriorityIdeas,
      completionRate: totalIdeas > 0 ? Math.round((completedIdeas / totalIdeas) * 100) : 0
    };
  };

  const stats = getStats();

  const recentIdeas = state.ideas
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 5);

  const getStatusBadge = (status: string) => {
    const badges = {
      idea: 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300',
      title: 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300',
      thumbnail: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300',
      script: 'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300',
      done: 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
    };
    return badges[status as keyof typeof badges] || badges.idea;
  };

  if (state.channels.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-md mx-auto"
        >
          <Youtube className="w-24 h-24 text-red-500 mx-auto mb-6" />
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Welcome to YouTube Empire Manager
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Create your first channel to start managing your YouTube content creation workflow with AI assistance.
          </p>
          <button
            onClick={() => dispatch({ type: 'OPEN_ADD_CHANNEL_MODAL' })}
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2 mx-auto"
          >
            <Plus className="w-5 h-5" />
            <span>Create Your First Channel</span>
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Overview of your YouTube Empire
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Channels
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {stats.totalChannels}
                </p>
              </div>
              <Youtube className="w-12 h-12 text-red-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Ideas
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {stats.totalIdeas}
                </p>
              </div>
              <TrendingUp className="w-12 h-12 text-blue-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  In Progress
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {stats.inProgressIdeas}
                </p>
              </div>
              <Clock className="w-12 h-12 text-yellow-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Completed
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {stats.completedIdeas}
                </p>
              </div>
              <CheckCircle className="w-12 h-12 text-green-500" />
            </div>
          </motion.div>
        </div>

        {/* Progress Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Completion Rate
              </h3>
              <BarChart3 className="w-5 h-5 text-gray-400" />
            </div>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Overall Progress</span>
                <span className="font-medium text-gray-900 dark:text-white">{stats.completionRate}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${stats.completionRate}%` }}
                  transition={{ delay: 0.8, duration: 1 }}
                  className="bg-green-500 h-2 rounded-full"
                />
              </div>
              <div className="grid grid-cols-2 gap-4 pt-2">
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Completed:</span>
                  <span className="ml-2 font-medium text-green-600 dark:text-green-400">{stats.completedIdeas}</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Remaining:</span>
                  <span className="ml-2 font-medium text-gray-600 dark:text-gray-400">{stats.totalIdeas - stats.completedIdeas}</span>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Priority Overview
              </h3>
              <Star className="w-5 h-5 text-gray-400" />
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">High Priority</span>
                <span className="px-2 py-1 bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300 rounded-full text-xs font-medium">
                  {stats.highPriorityIdeas}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">Total Folders</span>
                <span className="px-2 py-1 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-full text-xs font-medium">
                  {stats.totalFolders}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">Active Channels</span>
                <span className="px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded-full text-xs font-medium">
                  {stats.totalChannels}
                </span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Recent Ideas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Ideas
            </h3>
            <Calendar className="w-5 h-5 text-gray-400" />
          </div>
          
          {recentIdeas.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 text-center py-8">
              No ideas yet. Create your first idea to get started!
            </p>
          ) : (
            <div className="space-y-3">
              {recentIdeas.map((idea, index) => {
                const channel = state.channels.find(c => c.id === idea.channelId);
                const folder = state.folders.find(f => f.id === idea.folderId);
                
                return (
                  <motion.div
                    key={idea.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                    onClick={() => {
                      dispatch({ type: 'SELECT_CHANNEL', payload: idea.channelId });
                      dispatch({ type: 'SELECT_FOLDER', payload: idea.folderId });
                      dispatch({ type: 'SELECT_IDEA', payload: idea.id });
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: channel?.color || '#gray' }}
                      />
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {idea.name}
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {channel?.name} → {folder?.name}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadge(idea.status)}`}>
                        {idea.status}
                      </span>
                      <span className="text-xs text-gray-400">
                        {new Date(idea.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default DashboardView;
