{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:backend": "cd server && npm run dev", "dev:full": "concurrently \"npm run dev:backend\" \"npm run dev\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@faker-js/faker": "^9.9.0", "@supabase/supabase-js": "^2.52.0", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "concurrently": "^9.2.0", "framer-motion": "^12.23.3", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5"}}