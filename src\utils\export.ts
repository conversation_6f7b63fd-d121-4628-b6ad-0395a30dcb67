import { Channel, Folder, Idea } from '../types';

export const exportToMarkdown = (ideas: Idea[], channels: Channel[], folders: Folder[]) => {
  let markdown = '# YouTube Empire Manager Export\n\n';
  markdown += `Generated on: ${new Date().toLocaleString()}\n\n`;

  channels.forEach(channel => {
    markdown += `## ${channel.name}\n`;
    if (channel.description) {
      markdown += `*${channel.description}*\n`;
    }
    markdown += '\n';

    const channelFolders = folders.filter(f => f.channelId === channel.id);
    
    channelFolders.forEach(folder => {
      markdown += `### ${folder.name}\n\n`;
      
      const folderIdeas = ideas.filter(i => i.folderId === folder.id);
      
      folderIdeas.forEach(idea => {
        markdown += `#### ${idea.name}\n`;
        markdown += `- **Status:** ${idea.status}\n`;
        markdown += `- **Priority:** ${idea.priority}\n`;
        markdown += `- **Tags:** ${idea.tags.join(', ')}\n`;
        markdown += `- **Created:** ${new Date(idea.createdAt).toLocaleString()}\n\n`;
        
        if (idea.content.title) {
          markdown += `**Title:** ${idea.content.title}\n\n`;
        }
        
        if (idea.content.thumbnailDescription) {
          markdown += `**Thumbnail:** ${idea.content.thumbnailDescription}\n\n`;
        }
        
        if (idea.content.script) {
          markdown += `**Script:**\n\`\`\`\n${idea.content.script}\n\`\`\`\n\n`;
        }
        
        markdown += '---\n\n';
      });
    });
  });

  return markdown;
};

export const exportToJSON = (data: any) => {
  return JSON.stringify(data, null, 2);
};

export const downloadFile = (content: string, filename: string, type: string) => {
  const blob = new Blob([content], { type });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
