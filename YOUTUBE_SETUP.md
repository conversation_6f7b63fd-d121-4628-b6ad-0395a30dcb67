# YouTube Integration Setup Guide

This guide is for **developers** setting up the YouTube API integration. **End users don't need to do any of this** - they just click "Connect YouTube" and authenticate!

## For Developers: One-Time Setup

As the app developer, you'll set up the Google API credentials once, and all users will benefit from a seamless experience.

## Prerequisites

- Google Cloud Console account
- YouTube channel (optional, but recommended for testing)

## Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name: `YouTube Empire Manager`
4. Click "Create"

## Step 2: Enable YouTube Data API

1. In your Google Cloud project, go to "APIs & Services" → "Library"
2. Search for "YouTube Data API v3"
3. Click on it and press "Enable"

## Step 3: Create API Credentials

### Create API Key (for public data)
1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "API Key"
3. Copy the API key
4. Click "Restrict Key" and select "YouTube Data API v3"

### Create OAuth 2.0 Client (for user data)
1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth client ID"
3. If prompted, configure OAuth consent screen:
   - Choose "External" user type
   - Fill in app name: `YouTube Empire Manager`
   - Add your email as developer contact
   - Add scopes: `../auth/youtube.readonly`, `../auth/youtube.upload`, `../auth/youtube.force-ssl`
4. Choose "Web application" as application type
5. Add authorized redirect URIs:
   - `http://localhost:5173/auth/callback`
   - `http://127.0.0.1:5173/auth/callback`
6. Click "Create"
7. Copy the Client ID and Client Secret

## Step 4: Update Environment Variables

Update your `.env` file with the credentials:

```env
# YouTube API Configuration (Developer Setup - Users don't need this!)
VITE_YOUTUBE_API_KEY=your_api_key_here
VITE_GOOGLE_CLIENT_ID=your_client_id_here
VITE_GOOGLE_CLIENT_SECRET=your_client_secret_here
VITE_GOOGLE_REDIRECT_URI=http://localhost:5173/auth/callback
```

## For End Users: Simple Experience

Once you've completed the developer setup above, your users will have a **zero-configuration experience**:

1. ✅ Click "Connect YouTube" button
2. ✅ Authenticate with their Google account
3. ✅ Grant permission to access their YouTube data
4. ✅ Start managing their channels immediately!

**No API keys, no technical setup, no Google Cloud Console required for users!**

## Step 5: Test the Integration

1. Start your development server: `npm run dev`
2. Click "Connect YouTube" in the sidebar
3. Follow the authentication flow
4. Import your YouTube channels

## Features Available

### ✅ Channel Management
- Import existing YouTube channels
- View channel statistics (subscribers, videos, views)
- Sync channel metadata

### ✅ Video Import
- Import published videos as "done" ideas
- View video analytics (views, likes, comments)
- Track video performance

### ✅ Content Research
- Search YouTube for trending content
- Analyze competitor videos
- Get inspiration for new ideas

### ✅ Analytics Integration
- Real-time channel statistics
- Video performance tracking
- Growth metrics

## Security Notes

- **Read-only access**: The app only requests read permissions
- **OAuth 2.0**: Secure authentication through Google
- **Local storage**: Tokens are stored locally in your browser
- **Revoke access**: You can revoke access anytime in your Google account settings

## Troubleshooting

### "API not configured" error
- Make sure you've enabled YouTube Data API v3 in Google Cloud Console
- Check that your API key is correctly set in the `.env` file

### "Redirect URI mismatch" error
- Verify the redirect URI in Google Cloud Console matches exactly: `http://localhost:5173/auth/callback`
- Make sure you're accessing the app via `localhost:5173` (not `127.0.0.1`)

### "Quota exceeded" error
- YouTube API has daily quotas
- Each request consumes quota units
- For development, the free tier should be sufficient

### Authentication popup blocked
- Allow popups for your localhost domain
- Try disabling popup blockers temporarily

## API Quotas & Limits

- **Daily quota**: 10,000 units (free tier)
- **Channel list**: 1 unit per request
- **Video list**: 1 unit per request
- **Search**: 100 units per request

## Next Steps

Once set up, you can:

1. **Import channels**: Connect your existing YouTube channels
2. **Sync content**: Import published videos as completed ideas
3. **Research trends**: Use the search feature to find trending content
4. **Track performance**: Monitor your video analytics
5. **Plan content**: Use insights to plan future videos

## Support

If you encounter issues:

1. Check the browser console for error messages
2. Verify all environment variables are set correctly
3. Ensure your Google Cloud project has the YouTube API enabled
4. Check that OAuth consent screen is properly configured

Happy content creating! 🎬✨
