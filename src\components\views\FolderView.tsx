import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Lightbulb, 
  CheckCircle, 
  Clock, 
  Search,
  ArrowLeft,
  Settings
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { dataLoader } from '../../utils/dataLoader';
import { v4 as uuidv4 } from 'uuid';

const FolderView: React.FC = () => {
  const { state, dispatch } = useApp();
  const [showNewIdeaForm, setShowNewIdeaForm] = useState(false);
  const [newIdeaName, setNewIdeaName] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingFolderName, setEditingFolderName] = useState('');

  const currentFolder = state.folders.find(f => f.id === state.selectedFolderId);
  const currentChannel = state.channels.find(ch => ch.id === state.selectedChannelId);
  const folderIdeas = state.ideas.filter(i => i.folderId === state.selectedFolderId);

  if (!currentFolder || !currentChannel) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-gray-500 dark:text-gray-400">Select a folder to view its ideas.</p>
      </div>
    );
  }

  const handleCreateIdea = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newIdeaName.trim()) return;

    const newIdea = {
      id: uuidv4(),
      folderId: currentFolder.id,
      channelId: currentChannel.id,
      name: newIdeaName,
      status: 'idea' as const,
      tags: [],
      priority: 'medium' as const,
      content: {},
      promptHistory: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Optimistic update
    dispatch({ type: 'ADD_IDEA', payload: newIdea });
    setNewIdeaName('');
    setShowNewIdeaForm(false);

    // Save to Supabase
    const result = await dataLoader.createIdea(newIdea);
    
    if (!result) {
      // Rollback on error
      dispatch({ type: 'DELETE_IDEA', payload: newIdea.id });
      alert('Failed to create idea. Please try again.');
    }
  };

  const handleUpdateFolder = async () => {
    if (!currentFolder || !editingFolderName.trim()) return;

    const originalFolder = { ...currentFolder };
    const updatedFolder = {
      ...currentFolder,
      name: editingFolderName,
      updatedAt: new Date().toISOString()
    };

    // Optimistic update
    dispatch({ type: 'UPDATE_FOLDER', payload: updatedFolder });

    const result = await dataLoader.updateFolder(updatedFolder);

    if (!result) {
      console.error('Failed to update folder');
      dispatch({ type: 'UPDATE_FOLDER', payload: originalFolder });
      alert('Failed to update folder name. Please try again.');
    }

    setIsEditModalOpen(false);
    setEditingFolderName('');
  };

  const filteredIdeas = folderIdeas
    .filter(idea => {
      const matchesSearch = idea.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (idea.tags && idea.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())));
      const matchesStatus = filterStatus === 'all' || idea.status === filterStatus;
      const matchesPriority = filterPriority === 'all' || idea.priority === filterPriority;
      
      return matchesSearch && matchesStatus && matchesPriority;
    })
    .sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority];
      const bPriority = priorityOrder[b.priority];
      
      if (aPriority !== bPriority) return bPriority - aPriority;
      
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    });

  const getStatusColor = (status: string) => {
    const colors = {
      idea: 'text-gray-500 bg-gray-100 dark:bg-gray-700 dark:text-gray-400',
      title: 'text-blue-500 bg-blue-100 dark:bg-blue-900 dark:text-blue-300',
      thumbnail: 'text-yellow-500 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300',
      script: 'text-purple-500 bg-purple-100 dark:bg-purple-900 dark:text-purple-300',
      done: 'text-green-500 bg-green-100 dark:bg-green-900 dark:text-green-300'
    };
    return colors[status as keyof typeof colors] || colors.idea;
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      high: 'text-red-500 bg-red-100 dark:bg-red-900 dark:text-red-300',
      medium: 'text-yellow-500 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300',
      low: 'text-green-500 bg-green-100 dark:bg-green-900 dark:text-green-300'
    };
    return colors[priority as keyof typeof colors] || colors.medium;
  };

  const getProgressPercentage = (status: string) => {
    const statusProgress = { idea: 0, title: 25, thumbnail: 50, script: 75, done: 100 };
    return statusProgress[status as keyof typeof statusProgress] || 0;
  };

  const folderStats = {
    total: folderIdeas.length,
    completed: folderIdeas.filter(i => i.status === 'done').length,
    inProgress: folderIdeas.filter(i => i.status !== 'idea' && i.status !== 'done').length,
    ideas: folderIdeas.filter(i => i.status === 'idea').length
  };

  return (
    <div className="h-full flex flex-col p-6 bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <button
            onClick={() => dispatch({ type: 'SELECT_FOLDER', payload: null })}
            className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: currentChannel.color }}
          />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {currentFolder.name}
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {currentChannel.name} • {folderStats.total} {folderStats.total === 1 ? 'idea' : 'ideas'}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={() => {
              setEditingFolderName(currentFolder.name);
              setIsEditModalOpen(true);
            }}
            className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          >
            <Settings className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          <button
            onClick={() => setShowNewIdeaForm(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center space-x-2 shadow-sm"
          >
            <Plus className="w-4 h-4" />
            <span>New Idea</span>
          </button>
        </div>
      </header>

      {/* Edit Folder Modal */}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md shadow-xl">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Edit Folder Name</h2>
            <input
              type="text"
              value={editingFolderName}
              onChange={(e) => setEditingFolderName(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleUpdateFolder()}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-4"
              autoFocus
            />
            <div className="flex justify-end space-x-2">
              <button onClick={() => setIsEditModalOpen(false)} className="px-4 py-2 rounded-lg bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">Cancel</button>
              <button onClick={handleUpdateFolder} className="px-4 py-2 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors">Save</button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto -mx-6 px-6">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          {/* Stat cards ... */}
        </div>

        {/* New Idea Form */}
        {showNewIdeaForm && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-6"
          >
            <form onSubmit={handleCreateIdea} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Idea Name</label>
                <input
                  type="text"
                  value={newIdeaName}
                  onChange={(e) => setNewIdeaName(e.target.value)}
                  placeholder="e.g., How to Build a React App, iPhone 15 Review..."
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  autoFocus
                />
              </div>
              <div className="flex items-center space-x-3">
                <button type="submit" className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold">Create Idea</button>
                <button type="button" onClick={() => setShowNewIdeaForm(false)} className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg">Cancel</button>
              </div>
            </form>
          </motion.div>
        )}

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3.5 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search ideas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <select value={filterStatus} onChange={(e) => setFilterStatus(e.target.value)} className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="all">All Status</option>
            <option value="idea">Ideas</option>
            <option value="title">Title</option>
            <option value="thumbnail">Thumbnail</option>
            <option value="script">Script</option>
            <option value="done">Done</option>
          </select>
          <select value={filterPriority} onChange={(e) => setFilterPriority(e.target.value)} className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="all">All Priority</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>

        {/* Ideas List */}
        <div className="space-y-3">
          {filteredIdeas.length > 0 ? (
            filteredIdeas.map((idea, index) => (
              <motion.div
                key={idea.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.05 * index }}
                className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-500 transition-all cursor-pointer"
                onClick={() => dispatch({ type: 'SELECT_IDEA', payload: idea.id })}
              >
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-semibold text-gray-800 dark:text-white pr-4">{idea.name}</h3>
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(idea.priority)}`}>{idea.priority}</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(idea.status)}`}>{idea.status}</span>
                  </div>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mb-2">
                  <div className="bg-blue-500 h-1.5 rounded-full" style={{ width: `${getProgressPercentage(idea.status)}%` }}></div>
                </div>
                <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                  <span>Updated {new Date(idea.updatedAt).toLocaleDateString()}</span>
                  <div className="flex items-center space-x-2">
                    {idea.tags && idea.tags.slice(0, 2).map((tag, i) => (
                      <span key={i} className="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">{tag}</span>
                    ))}
                    {idea.tags && idea.tags.length > 2 && (
                      <span>+{idea.tags.length - 2}</span>
                    )}
                  </div>
                </div>
              </motion.div>
            ))
          ) : (
            <div className="text-center py-16">
              <Lightbulb className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">No ideas found</h3>
              <p className="text-gray-500 dark:text-gray-400">Try adjusting your filters or create a new idea.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FolderView;
